Bir Apartman ve Site Yönetim Sistemi geliştirmek istiyorum. Sistem masaüstü uygulaması olacak, C# (.NET, tercihen WPF veya MAUI) kullanacağım. Veritabanı olarak Firebase Firestore kullanılacak.

Sistemin özellikleri:

Çoklu kullanıcı ve rol yönetimi olacak (Admin / Yönetici / Görüntüleyici vb.)

Kullanıcı giriş ekranı olacak (email + şifre ile giriş, Firebase Authentication kullanılabilir)

Kullanıcı giriş yaptıktan sonra, kendisine ait olan veya yetkili olduğu "Site" veya "Apartman" yönetim ekranlarına erişebilecek.

Veri yapısı:

Bir kullanıcı birden fazla "Site" veya "Apartman" yönetebilecek.

Her Site içinde bir veya daha fazla Apartman olacak.

Her Apartman içinde bir veya daha fazla Daire olacak.

Her Daire'ye bir Kiracı atanabilecek.

Kiracı bilgileri: Ad, <PERSON>d, Telefon, Email, <PERSON><PERSON><PERSON>, <PERSON>ık<PERSON>ş <PERSON>.

Kira Bedeli ve Aidat bilgileri tutulacak.

Kira ve Aidat ödeme durumu (Ödendi / Borçlu) tutulacak.

Ödeme geçmişi takip edilecek.

Bildirim sistemi:

Email ve SMS ile belirlenen tarihlerde (örneğin ödeme günü yaklaşırken) otomatik bildirim gönderilecek.

Gereksinimler:

Kullanılacak C# kütüphanelerini öner (Firebase bağlantısı, UI, email/SMS için gerekli kütüphaneler)

Firestore için veritabanı şemasını öner

Geliştirme adımlarını sırayla planla (öncelik sırasına göre)

Örnek arayüz akış diyagramı veya ekran yapısı öner

Temiz ve sürdürülebilir bir proje yapısı için mimari öner (örneğin MVVM)

1️⃣ İlk Yapılacaklar Listesi (Milestone / Yol Haritası)
Aşama	Yapılacak İş
1️⃣	Firebase projesi oluştur (Firestore + Authentication aktif et)
2️⃣	C# Desktop app (WPF veya MAUI) projesi başlat
3️⃣	Firebase bağlantısı kur (Firestore + Authentication kütüphaneleri yükle)
4️⃣	Kullanıcı giriş ve rol yönetimi ekranı hazırla
5️⃣	"Site" ve "Apartman" yönetim ekranları oluştur
6️⃣	"Daire" ve "Kiracı" yönetim ekranları hazırla
7️⃣	Kira ve Aidat ödeme takip ekranları yap
8️⃣	Ödeme geçmişi ve rapor ekranları ekle
9️⃣	Bildirim sistemi için Email ve SMS altyapısı kur (örn. SMTP + Twilio)
🔟	Son test ve deploy


2️⃣ Firebase Firestore Veritabanı Yapısı (Tavsiye Edilen Tasarım)
yaml
Kopyala
Düzenle
Users/
  UserID/
    Email
    Role (Admin, Yönetici, Görüntüleyici)
    Name
    AssignedSites [SiteID1, SiteID2,...]

Sites/
  SiteID/
    SiteName
    Address
    Managers [UserID1, UserID2,...]
    Apartments/
      ApartmentID/
        ApartmentName
        Daireler/
          DaireID/
            DaireNo
            Kiraci/
              Ad
              Soyad
              Telefon
              Email
              GirisTarihi
              CikisTarihi
            KiraBedeli
            KiraDurumu (Odendi / Borclu)
            Aidat
            AidatDurumu (Odendi / Borclu)
            OdemeGecmisi/
              PaymentID/
                Tarih
                Tutar
                Tip (Kira / Aidat)
                OdemeDurumu
3️⃣ Kullanılacak C# Kütüphaneleri

| İhtiyaç                | Kütüphane / Teknoloji                                                                                                                          |
| ---------------------- |------------------------------------------------------------------------------------------------------------------------------------------------|
| Firebase bağlantısı    | [FirebaseAdmin](https://www.nuget.org/packages/FirebaseAdmin), [Google.Cloud.Firestore](https://www.nuget.org/packages/Google.Cloud.Firestore) |
| Authentication (Giriş) | Firebase Authentication SDK + REST API                                                                                                         |
| UI / Arayüz            | WPF (.NET 8), alternatif olarak MAUI (multi-platform)                                                                                          |
| MVVM yapısı            | CommunityToolkit.Mvvm                                                                                                                          |
| Email gönderimi        | System.Net.Mail (SMTP), MailKit (daha gelişmiş SMTP desteği)                                                                                   |
| SMS gönderimi          | Twilio C# SDK veya alternatif SMS sağlayıcı SDK’sı                                                                                             |
| Logging                | Serilog                                                                                                                                        |


[Login Screen] --> [Dashboard (Kullanıcının yetkili olduğu Siteler listesi)]  
    --> [Site Detayı] --> [Apartman Listesi]  
        --> [Apartman Detayı] --> [Daire Listesi]  
            --> [Daire Detayı] --> [Kiracı Bilgisi + Ödeme Geçmişi]  
                --> [Yeni Ödeme Ekle / Bildirim Gönder]  


5️⃣ Önerilen Mimari
MVVM (Model - View - ViewModel)

Repository Pattern (Veri erişimi için)

Dependency Injection (Service registration)

Singleton kullanımı (Firebase servisleri için)

Arayüzde async/await destekli veri yükleme


Ve şuanda  firestore bağlantısı yapabilmek için gerekli bilgiler :
[apartmanyonetimi-ab3d7-firebase-adminsdk-fbsvc-cd63bef3c0.json](../../Downloads/apartmanyonetimi-ab3d7-firebase-adminsdk-fbsvc-cd63bef3c0.json)
dosyasında.