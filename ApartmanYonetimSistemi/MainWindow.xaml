<Window x:Class="ApartmanYonetimSistemi.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ApartmanYonetimSistemi"
        xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
        mc:Ignorable="d"
        Title="Apartman Yönetim Sistemi - Dashboard" 
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">
    
    <Window.DataContext>
        <vm:DashboardViewModel />
    </Window.DataContext>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2E86AB" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Welcome Message -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="🏢" FontSize="24" Margin="0,0,10,0"/>
                    <StackPanel>
                        <TextBlock Text="Apartman Yönetim Sistemi" 
                                  FontSize="20" FontWeight="Bold" 
                                  Foreground="White"/>
                        <TextBlock Text="{Binding WelcomeMessage}" 
                                  FontSize="14" 
                                  Foreground="#E3F2FD"/>
                    </StackPanel>
                </StackPanel>

                <!-- User Actions -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="{Binding CurrentUser.Name}" 
                              FontSize="14" Foreground="White" 
                              VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <Button Content="Çıkış" 
                            Command="{Binding LogoutCommand}"
                            Background="#1976D2" Foreground="White"
                            BorderThickness="0" Padding="15,8"
                            FontSize="12" Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="4" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" 
                                                                VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#1565C0"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Action Bar -->
            <Border Grid.Row="0" Background="White" 
                    Padding="20,15" Margin="0,0,0,20"
                    CornerRadius="8"
                    Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="📋" FontSize="20" Margin="0,0,10,0"/>
                        <TextBlock Text="Site Yönetimi" FontSize="18" FontWeight="SemiBold" 
                                  Foreground="#2E86AB" VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Content="📊 Raporlar"
                                Command="{Binding OpenReportsCommand}"
                                Background="#FF9800" Foreground="White"
                                BorderThickness="0" Padding="15,10"
                                FontSize="12" FontWeight="SemiBold" Cursor="Hand" Margin="0,0,10,0">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                    VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#F57C00"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>

                        <Button Content="+ Yeni Site Ekle"
                                Command="{Binding CreateNewSiteCommand}"
                                Background="#4CAF50" Foreground="White"
                                BorderThickness="0" Padding="20,10"
                                FontSize="14" FontWeight="SemiBold" Cursor="Hand">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                    VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#45A049"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Statistics Cards -->
            <Grid Grid.Row="1" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Sites -->
                <Border Grid.Column="0" Background="White" CornerRadius="8" Padding="20" Margin="0,0,10,0"
                        Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="🏢" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding TotalSites}" FontSize="28" FontWeight="Bold"
                                  Foreground="#2E86AB" HorizontalAlignment="Center"/>
                        <TextBlock Text="Site" FontSize="12" Foreground="#666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Total Apartments -->
                <Border Grid.Column="1" Background="White" CornerRadius="8" Padding="20" Margin="0,0,10,0"
                        Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="🏠" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding TotalApartments}" FontSize="28" FontWeight="Bold"
                                  Foreground="#4CAF50" HorizontalAlignment="Center"/>
                        <TextBlock Text="Apartman" FontSize="12" Foreground="#666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Total Daireler -->
                <Border Grid.Column="2" Background="White" CornerRadius="8" Padding="20" Margin="0,0,10,0"
                        Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="🚪" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding TotalDaireler}" FontSize="28" FontWeight="Bold"
                                  Foreground="#FF9800" HorizontalAlignment="Center"/>
                        <TextBlock Text="Daire" FontSize="12" Foreground="#666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Occupied Daireler -->
                <Border Grid.Column="3" Background="White" CornerRadius="8" Padding="20" Margin="0,0,10,0"
                        Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="👤" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding OccupiedDaireler}" FontSize="28" FontWeight="Bold"
                                  Foreground="#9C27B0" HorizontalAlignment="Center"/>
                        <TextBlock Text="Dolu" FontSize="12" Foreground="#666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Monthly Income -->
                <Border Grid.Column="4" Background="White" CornerRadius="8" Padding="20" Margin="0,0,10,0"
                        Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="💰" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding MonthlyIncome, StringFormat={}{0:C0}}" FontSize="20" FontWeight="Bold"
                                  Foreground="#4CAF50" HorizontalAlignment="Center"/>
                        <TextBlock Text="Aylık Gelir" FontSize="12" Foreground="#666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Borclu Daireler -->
                <Border Grid.Column="5" Background="White" CornerRadius="8" Padding="20"
                        Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="⚠️" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding BorcluDaireler}" FontSize="28" FontWeight="Bold"
                                  Foreground="#F44336" HorizontalAlignment="Center"/>
                        <TextBlock Text="Borçlu" FontSize="12" Foreground="#666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Sites List -->
            <Border Grid.Row="2" Background="White"
                    CornerRadius="8" Padding="20"
                    Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                
                <Grid>
                    <!-- Loading Indicator -->
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                               Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock Text="⏳" FontSize="32" HorizontalAlignment="Center"/>
                        <TextBlock Text="Siteler yükleniyor..." FontSize="14" 
                                  Foreground="#666666" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                    </StackPanel>

                    <!-- Sites Grid -->
                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                 Visibility="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}">
                        <ItemsControl ItemsSource="{Binding UserSites}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel Orientation="Horizontal"/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="#F8F9FA" 
                                            BorderBrush="#E0E0E0" BorderThickness="1"
                                            CornerRadius="8" Margin="10" Padding="20"
                                            Width="280" Height="160" Cursor="Hand">
                                        <Border.InputBindings>
                                            <MouseBinding MouseAction="LeftClick" 
                                                         Command="{Binding DataContext.SelectSiteCommand, 
                                                                  RelativeSource={RelativeSource AncestorType=Window}}"
                                                         CommandParameter="{Binding}"/>
                                        </Border.InputBindings>
                                        
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#E3F2FD"/>
                                                        <Setter Property="BorderBrush" Value="#2E86AB"/>
                                                    </Trigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>

                                        <StackPanel>
                                            <TextBlock Text="🏢" FontSize="32" 
                                                      HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                            <TextBlock Text="{Binding SiteName}" 
                                                      FontSize="16" FontWeight="SemiBold"
                                                      HorizontalAlignment="Center" 
                                                      TextWrapping="Wrap" Margin="0,0,0,5"/>
                                            <TextBlock Text="{Binding Address}" 
                                                      FontSize="12" Foreground="#666666"
                                                      HorizontalAlignment="Center" 
                                                      TextWrapping="Wrap"/>
                                            <TextBlock Text="Detayları Görüntüle →" 
                                                      FontSize="11" Foreground="#2E86AB"
                                                      HorizontalAlignment="Center" 
                                                      Margin="0,10,0,0"/>
                                        </StackPanel>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>

                    <!-- Empty State -->
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                               Visibility="{Binding UserSites.Count, Converter={StaticResource ZeroToVisibilityConverter}}">
                        <TextBlock Text="📋" FontSize="48" HorizontalAlignment="Center" Opacity="0.5"/>
                        <TextBlock Text="Henüz site bulunmuyor" FontSize="18" 
                                  Foreground="#666666" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                        <TextBlock Text="Yeni bir site eklemek için yukarıdaki butonu kullanın" 
                                  FontSize="12" Foreground="#999999" 
                                  HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Window>
