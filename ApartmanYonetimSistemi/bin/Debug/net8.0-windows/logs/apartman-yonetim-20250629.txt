2025-06-29 15:51:26.849 +03:00 [INF] Apart<PERSON>ıyor...
2025-06-29 15:51:28.864 +03:00 [ERR] Failed to initialize Firebase
System.InvalidOperationException: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc.
   at Google.Apis.Auth.OAuth2.DefaultCredentialProvider.CreateDefaultCredentialAsync()
   at Google.Api.Gax.Grpc.DefaultChannelCredentialsCache.<.ctor>b__5_1()
   at Google.Api.Gax.Grpc.DefaultChannelCredentialsCache.GetCredentialsAsync(String universeDomain, CancellationToken cancellationToken)
   at Google.Api.Gax.TaskExtensions.WaitWithUnwrappedExceptions(Task task)
   at Google.Api.Gax.TaskExtensions.ResultWithUnwrappedExceptions[T](Task`1 task)
   at Google.Api.Gax.Grpc.DefaultChannelCredentialsCache.GetCredentials(String universeDomain)
   at Google.Api.Gax.Grpc.ChannelPool.GetChannel(GrpcAdapter grpcAdapter, String universeDomain, String endpoint, GrpcChannelOptions channelOptions)
   at Google.Api.Gax.Grpc.ClientBuilderBase`1.CreateCallInvoker()
   at Google.Cloud.Firestore.V1.FirestoreClientBuilder.BuildImpl()
   at Google.Cloud.Firestore.V1.FirestoreClientBuilder.Build()
   at Google.Cloud.Firestore.FirestoreDbBuilder.Build()
   at Google.Cloud.Firestore.FirestoreDb.Create(String projectId, FirestoreClient client)
   at ApartmanYonetimSistemi.Services.FirebaseService.InitializeFirebase() in C:\Users\<USER>\Desktop\PycharmProjects\Apartman_Yonetim_Sistemi\ApartmanYonetimSistemi\Services\FirebaseService.cs:line 95
2025-06-29 15:51:28.971 +03:00 [FTL] Unhandled exception occurred
System.Windows.Markup.XamlParseException: ''System.Windows.UIElement.Effect' özelliğini ayarlama işlemi özel durum döndürdü.' Satır numarası '36' ve satır konumu '17'.
 ---> System.InvalidOperationException: 'True', 'Effect' özelliği için geçerli bir değer değil.
   at System.Windows.DependencyObject.EvaluateExpression(EntryIndex entryIndex, DependencyProperty dp, Expression expr, PropertyMetadata metadata, EffectiveValueEntry oldEntry, EffectiveValueEntry newEntry)
   at System.Windows.DependencyObject.SetValueCommon(DependencyProperty dp, Object value, PropertyMetadata metadata, Boolean coerceWithDeferredReference, Boolean coerceWithCurrentValue, OperationType operationType, Boolean isInternal)
   at System.Windows.DependencyObject.SetValue(DependencyProperty dp, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.SetValue(Object inst, XamlMember property, Object value)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadBamlStreamWithSyncInfo(Stream stream, ParserContext pc)
   at System.Windows.Application.DoStartup()
   at System.Windows.Application.<.ctor>b__1_0(Object unused)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
