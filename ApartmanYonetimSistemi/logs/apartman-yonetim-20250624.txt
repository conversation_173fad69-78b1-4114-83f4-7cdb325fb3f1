2025-06-24 16:04:19.311 +03:00 [INF] Apartman Yönetim <PERSON> başlatılıyor...
2025-06-24 16:04:19.456 +03:00 [ERR] Failed to initialize Firebase
System.IO.FileNotFoundException: Firebase credential file not found: C:\Users\<USER>\PycharmProjects\Apartman_Yonetim_Sistemi\ApartmanYonetimSistemi\bin\Debug\net8.0-windows\apartmanyonetimi-ab3d7-firebase-adminsdk-fbsvc-cd63bef3c0.json
   at ApartmanYonetimSistemi.Services.FirebaseService.InitializeFirebase() in C:\Users\<USER>\PycharmProjects\Apartman_Yonetim_Sistemi\ApartmanYonetimSistemi\Services\FirebaseService.cs:line 57
2025-06-24 16:11:27.538 +03:00 [INF] Apartman Yönetim Sistemi başlatılıyor...
2025-06-24 16:11:27.639 +03:00 [ERR] Failed to initialize Firebase
System.IO.FileNotFoundException: Firebase credential file not found: C:\Users\<USER>\PycharmProjects\Apartman_Yonetim_Sistemi\ApartmanYonetimSistemi\bin\Debug\net8.0-windows\apartmanyonetimi-ab3d7-firebase-adminsdk-fbsvc-cd63bef3c0.json
   at ApartmanYonetimSistemi.Services.FirebaseService.InitializeFirebase() in C:\Users\<USER>\PycharmProjects\Apartman_Yonetim_Sistemi\ApartmanYonetimSistemi\Services\FirebaseService.cs:line 57
2025-06-24 16:17:51.679 +03:00 [INF] Apartman Yönetim Sistemi başlatılıyor...
2025-06-24 16:17:54.056 +03:00 [ERR] Failed to initialize Firebase
System.InvalidOperationException: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc.
   at Google.Apis.Auth.OAuth2.DefaultCredentialProvider.CreateDefaultCredentialAsync()
   at Google.Api.Gax.Grpc.DefaultChannelCredentialsCache.<.ctor>b__5_1()
   at Google.Api.Gax.Grpc.DefaultChannelCredentialsCache.GetCredentialsAsync(String universeDomain, CancellationToken cancellationToken)
   at Google.Api.Gax.TaskExtensions.WaitWithUnwrappedExceptions(Task task)
   at Google.Api.Gax.TaskExtensions.ResultWithUnwrappedExceptions[T](Task`1 task)
   at Google.Api.Gax.Grpc.DefaultChannelCredentialsCache.GetCredentials(String universeDomain)
   at Google.Api.Gax.Grpc.ChannelPool.GetChannel(GrpcAdapter grpcAdapter, String universeDomain, String endpoint, GrpcChannelOptions channelOptions)
   at Google.Api.Gax.Grpc.ClientBuilderBase`1.CreateCallInvoker()
   at Google.Cloud.Firestore.V1.FirestoreClientBuilder.BuildImpl()
   at Google.Cloud.Firestore.V1.FirestoreClientBuilder.Build()
   at Google.Cloud.Firestore.FirestoreDbBuilder.Build()
   at Google.Cloud.Firestore.FirestoreDb.Create(String projectId, FirestoreClient client)
   at ApartmanYonetimSistemi.Services.FirebaseService.InitializeFirebase() in C:\Users\<USER>\PycharmProjects\Apartman_Yonetim_Sistemi\ApartmanYonetimSistemi\Services\FirebaseService.cs:line 71
2025-06-24 16:18:38.829 +03:00 [INF] Apartman Yönetim Sistemi başlatılıyor...
2025-06-24 16:18:40.903 +03:00 [ERR] Failed to initialize Firebase
System.InvalidOperationException: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc.
   at Google.Apis.Auth.OAuth2.DefaultCredentialProvider.CreateDefaultCredentialAsync()
   at Google.Api.Gax.Grpc.DefaultChannelCredentialsCache.<.ctor>b__5_1()
   at Google.Api.Gax.Grpc.DefaultChannelCredentialsCache.GetCredentialsAsync(String universeDomain, CancellationToken cancellationToken)
   at Google.Api.Gax.TaskExtensions.WaitWithUnwrappedExceptions(Task task)
   at Google.Api.Gax.TaskExtensions.ResultWithUnwrappedExceptions[T](Task`1 task)
   at Google.Api.Gax.Grpc.DefaultChannelCredentialsCache.GetCredentials(String universeDomain)
   at Google.Api.Gax.Grpc.ChannelPool.GetChannel(GrpcAdapter grpcAdapter, String universeDomain, String endpoint, GrpcChannelOptions channelOptions)
   at Google.Api.Gax.Grpc.ClientBuilderBase`1.CreateCallInvoker()
   at Google.Cloud.Firestore.V1.FirestoreClientBuilder.BuildImpl()
   at Google.Cloud.Firestore.V1.FirestoreClientBuilder.Build()
   at Google.Cloud.Firestore.FirestoreDbBuilder.Build()
   at Google.Cloud.Firestore.FirestoreDb.Create(String projectId, FirestoreClient client)
   at ApartmanYonetimSistemi.Services.FirebaseService.InitializeFirebase() in C:\Users\<USER>\PycharmProjects\Apartman_Yonetim_Sistemi\ApartmanYonetimSistemi\Services\FirebaseService.cs:line 71
