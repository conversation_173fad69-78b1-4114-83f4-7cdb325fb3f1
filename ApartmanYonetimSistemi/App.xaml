<Application x:Class="ApartmanYonetimSistemi.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:ApartmanYonetimSistemi"
             xmlns:helpers="clr-namespace:ApartmanYonetimSistemi.Helpers"
             StartupUri="Views/LoginWindow.xaml">
    <Application.Resources>
        <!-- Converters -->
        <helpers:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <helpers:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <helpers:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <helpers:ZeroToVisibilityConverter x:Key="ZeroToVisibilityConverter"/>
        <helpers:EnumToVisibilityConverter x:Key="EnumToVisibilityConverter"/>
    </Application.Resources>
</Application>
