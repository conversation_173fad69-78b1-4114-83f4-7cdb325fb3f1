{"version": 3, "targets": {"net8.0-windows7.0": {"CommunityToolkit.Mvvm/8.4.0": {"type": "package", "compile": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/CommunityToolkit.Mvvm.targets": {}}}, "FirebaseAdmin/3.2.0": {"type": "package", "dependencies": {"Google.Api.Gax.Rest": "4.8.0", "Google.Apis.Auth": "1.68.0", "System.Collections.Immutable": "8.0.0"}, "compile": {"lib/net6.0/FirebaseAdmin.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/FirebaseAdmin.dll": {"related": ".xml"}}}, "Google.Api.CommonProtos/2.16.0": {"type": "package", "dependencies": {"Google.Protobuf": "[3.28.2, 4.0.0)"}, "compile": {"lib/netstandard2.0/Google.Api.CommonProtos.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Google.Api.CommonProtos.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}}, "Google.Api.Gax/4.9.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Newtonsoft.Json": "13.0.3", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "compile": {"lib/netstandard2.0/Google.Api.Gax.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.dll": {"related": ".pdb;.xml"}}}, "Google.Api.Gax.Grpc/4.9.0": {"type": "package", "dependencies": {"Google.Api.CommonProtos": "2.16.0", "Google.Api.Gax": "4.9.0", "Google.Apis.Auth": "1.68.0", "Grpc.Auth": "2.66.0", "Grpc.Core.Api": "2.66.0", "Grpc.Net.Client": "2.66.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "compile": {"lib/netstandard2.0/Google.Api.Gax.Grpc.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.Grpc.dll": {"related": ".pdb;.xml"}}}, "Google.Api.Gax.Rest/4.8.0": {"type": "package", "dependencies": {"Google.Api.Gax": "4.8.0", "Google.Apis.Auth": "[1.67.0, 2.0.0)", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "compile": {"lib/netstandard2.0/Google.Api.Gax.Rest.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.Rest.dll": {"related": ".pdb;.xml"}}}, "Google.Apis/1.68.0": {"type": "package", "dependencies": {"Google.Apis.Core": "1.68.0"}, "compile": {"lib/net6.0/Google.Apis.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Google.Apis.dll": {"related": ".pdb;.xml"}}}, "Google.Apis.Auth/1.68.0": {"type": "package", "dependencies": {"Google.Apis": "1.68.0", "Google.Apis.Core": "1.68.0", "System.Management": "7.0.2"}, "compile": {"lib/net6.0/Google.Apis.Auth.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Google.Apis.Auth.dll": {"related": ".pdb;.xml"}}}, "Google.Apis.Core/1.68.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.3"}, "compile": {"lib/net6.0/Google.Apis.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Google.Apis.Core.dll": {"related": ".pdb;.xml"}}}, "Google.Cloud.Firestore/3.10.0": {"type": "package", "dependencies": {"Google.Cloud.Firestore.V1": "3.10.0", "System.Collections.Immutable": "6.0.0", "System.Linq.Async": "6.0.1"}, "compile": {"lib/netstandard2.0/Google.Cloud.Firestore.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Google.Cloud.Firestore.dll": {"related": ".pdb;.xml"}}}, "Google.Cloud.Firestore.V1/3.10.0": {"type": "package", "dependencies": {"Google.Api.Gax.Grpc": "[4.9.0, 5.0.0)", "Google.Cloud.Location": "[2.3.0, 3.0.0)", "Google.LongRunning": "[3.3.0, 4.0.0)"}, "compile": {"lib/netstandard2.0/Google.Cloud.Firestore.V1.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Google.Cloud.Firestore.V1.dll": {"related": ".pdb;.xml"}}}, "Google.Cloud.Location/2.3.0": {"type": "package", "dependencies": {"Google.Api.Gax.Grpc": "[4.8.0, 5.0.0)"}, "compile": {"lib/netstandard2.0/Google.Cloud.Location.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Google.Cloud.Location.dll": {"related": ".pdb;.xml"}}}, "Google.LongRunning/3.3.0": {"type": "package", "dependencies": {"Google.Api.Gax.Grpc": "[4.8.0, 5.0.0)"}, "compile": {"lib/netstandard2.0/Google.LongRunning.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Google.LongRunning.dll": {"related": ".pdb;.xml"}}}, "Google.Protobuf/3.28.2": {"type": "package", "compile": {"lib/net5.0/Google.Protobuf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Google.Protobuf.dll": {"related": ".pdb;.xml"}}}, "Grpc.Auth/2.66.0": {"type": "package", "dependencies": {"Google.Apis.Auth": "1.68.0", "Grpc.Core.Api": "2.66.0"}, "compile": {"lib/netstandard2.0/Grpc.Auth.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Grpc.Auth.dll": {"related": ".pdb;.xml"}}}, "Grpc.Core.Api/2.66.0": {"type": "package", "compile": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"related": ".pdb;.xml"}}}, "Grpc.Net.Client/2.66.0": {"type": "package", "dependencies": {"Grpc.Net.Common": "2.66.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0"}, "compile": {"lib/net8.0/Grpc.Net.Client.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Grpc.Net.Client.dll": {"related": ".pdb;.xml"}}}, "Grpc.Net.Common/2.66.0": {"type": "package", "dependencies": {"Grpc.Core.Api": "2.66.0"}, "compile": {"lib/net8.0/Grpc.Net.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Grpc.Net.Common.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.VisualBasic/10.3.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Serilog/4.3.0": {"type": "package", "compile": {"lib/net8.0/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.dll": {"related": ".xml"}}, "build": {"build/Serilog.targets": {}}}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/7.0.0": {"type": "package", "dependencies": {"Serilog": "4.2.0"}, "compile": {"lib/net8.0/Serilog.Sinks.File.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"related": ".xml"}}}, "System.CodeDom/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Collections.Immutable/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Linq.Async/6.0.1": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0"}, "compile": {"ref/net6.0/System.Linq.Async.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Linq.Async.dll": {"related": ".xml"}}}, "System.Management/7.0.2": {"type": "package", "dependencies": {"System.CodeDom": "7.0.0"}, "compile": {"lib/net7.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}}}, "libraries": {"CommunityToolkit.Mvvm/8.4.0": {"sha512": "tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "type": "package", "path": "communitytoolkit.mvvm/8.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "build/CommunityToolkit.Mvvm.FeatureSwitches.targets", "build/CommunityToolkit.Mvvm.SourceGenerators.targets", "build/CommunityToolkit.Mvvm.Windows.targets", "build/CommunityToolkit.Mvvm.WindowsSdk.targets", "build/CommunityToolkit.Mvvm.targets", "buildTransitive/CommunityToolkit.Mvvm.FeatureSwitches.targets", "buildTransitive/CommunityToolkit.Mvvm.SourceGenerators.targets", "buildTransitive/CommunityToolkit.Mvvm.Windows.targets", "buildTransitive/CommunityToolkit.Mvvm.WindowsSdk.targets", "buildTransitive/CommunityToolkit.Mvvm.targets", "communitytoolkit.mvvm.8.4.0.nupkg.sha512", "communitytoolkit.mvvm.nuspec", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.dll", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.pdb", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.xml", "lib/net8.0/CommunityToolkit.Mvvm.dll", "lib/net8.0/CommunityToolkit.Mvvm.pdb", "lib/net8.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.0/CommunityToolkit.Mvvm.dll", "lib/netstandard2.0/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.1/CommunityToolkit.Mvvm.dll", "lib/netstandard2.1/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.1/CommunityToolkit.Mvvm.xml"]}, "FirebaseAdmin/3.2.0": {"sha512": "689HWz59pmUQVJY3YJd7rsevICX8KL2qQX23XyHIp2+Qek9IScgL6T/AnyHF6WMirXll2tULRe+LGOItj7kF6A==", "type": "package", "path": "firebaseadmin/3.2.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "firebaseadmin.3.2.0.nupkg.sha512", "firebaseadmin.nuspec", "lib/net462/FirebaseAdmin.dll", "lib/net462/FirebaseAdmin.xml", "lib/net6.0/FirebaseAdmin.dll", "lib/net6.0/FirebaseAdmin.xml", "lib/netstandard2.0/FirebaseAdmin.dll", "lib/netstandard2.0/FirebaseAdmin.xml"]}, "Google.Api.CommonProtos/2.16.0": {"sha512": "37MuZrE9AAqHAdYgFLoTHydAiXDRriQZGVKEg6fr6ASnrY5GtauYXnQrGk5x2K3NmYzEXe+wkpaPVmxjb3NKjg==", "type": "package", "path": "google.api.commonprotos/2.16.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "NuGetIcon.png", "build/Google.Api.CommonProtos.targets", "content/protos/google/api/annotations.proto", "content/protos/google/api/auth.proto", "content/protos/google/api/backend.proto", "content/protos/google/api/billing.proto", "content/protos/google/api/client.proto", "content/protos/google/api/config_change.proto", "content/protos/google/api/consumer.proto", "content/protos/google/api/context.proto", "content/protos/google/api/control.proto", "content/protos/google/api/distribution.proto", "content/protos/google/api/documentation.proto", "content/protos/google/api/endpoint.proto", "content/protos/google/api/error_reason.proto", "content/protos/google/api/field_behavior.proto", "content/protos/google/api/field_info.proto", "content/protos/google/api/http.proto", "content/protos/google/api/httpbody.proto", "content/protos/google/api/label.proto", "content/protos/google/api/launch_stage.proto", "content/protos/google/api/log.proto", "content/protos/google/api/logging.proto", "content/protos/google/api/metric.proto", "content/protos/google/api/monitored_resource.proto", "content/protos/google/api/monitoring.proto", "content/protos/google/api/policy.proto", "content/protos/google/api/quota.proto", "content/protos/google/api/resource.proto", "content/protos/google/api/routing.proto", "content/protos/google/api/service.proto", "content/protos/google/api/source_info.proto", "content/protos/google/api/system_parameter.proto", "content/protos/google/api/usage.proto", "content/protos/google/api/visibility.proto", "content/protos/google/rpc/code.proto", "content/protos/google/rpc/context/attribute_context.proto", "content/protos/google/rpc/context/audit_context.proto", "content/protos/google/rpc/error_details.proto", "content/protos/google/rpc/http.proto", "content/protos/google/rpc/status.proto", "content/protos/google/type/calendar_period.proto", "content/protos/google/type/color.proto", "content/protos/google/type/date.proto", "content/protos/google/type/datetime.proto", "content/protos/google/type/dayofweek.proto", "content/protos/google/type/decimal.proto", "content/protos/google/type/expr.proto", "content/protos/google/type/fraction.proto", "content/protos/google/type/interval.proto", "content/protos/google/type/latlng.proto", "content/protos/google/type/localized_text.proto", "content/protos/google/type/money.proto", "content/protos/google/type/month.proto", "content/protos/google/type/phone_number.proto", "content/protos/google/type/postal_address.proto", "content/protos/google/type/quaternion.proto", "content/protos/google/type/timeofday.proto", "google.api.commonprotos.2.16.0.nupkg.sha512", "google.api.commonprotos.nuspec", "lib/net461/Google.Api.CommonProtos.dll", "lib/net461/Google.Api.CommonProtos.pdb", "lib/net461/Google.Api.CommonProtos.xml", "lib/netstandard2.0/Google.Api.CommonProtos.dll", "lib/netstandard2.0/Google.Api.CommonProtos.pdb", "lib/netstandard2.0/Google.Api.CommonProtos.xml"]}, "Google.Api.Gax/4.9.0": {"sha512": "fjHHYcQ99u0ztqwT537rvVtJMdDy6G2VHBZ+F1cBjDGYNVZfrpk40DMQ/OpUGToT9ZGHVirhh3eJ73bw2ANVPQ==", "type": "package", "path": "google.api.gax/4.9.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "NuGetIcon.png", "google.api.gax.4.9.0.nupkg.sha512", "google.api.gax.nuspec", "lib/net462/Google.Api.Gax.dll", "lib/net462/Google.Api.Gax.pdb", "lib/net462/Google.Api.Gax.xml", "lib/netstandard2.0/Google.Api.Gax.dll", "lib/netstandard2.0/Google.Api.Gax.pdb", "lib/netstandard2.0/Google.Api.Gax.xml"]}, "Google.Api.Gax.Grpc/4.9.0": {"sha512": "ToCx/0cs+wJ9j7vzKRcPAKneJVZrz/s9JhW9QsFx1dar9WzTxawQZ8xTjyieSy8tY0UiYCL1qYkn/iRrklYnSA==", "type": "package", "path": "google.api.gax.grpc/4.9.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "NuGetIcon.png", "google.api.gax.grpc.4.9.0.nupkg.sha512", "google.api.gax.grpc.nuspec", "lib/net462/Google.Api.Gax.Grpc.dll", "lib/net462/Google.Api.Gax.Grpc.pdb", "lib/net462/Google.Api.Gax.Grpc.xml", "lib/netstandard2.0/Google.Api.Gax.Grpc.dll", "lib/netstandard2.0/Google.Api.Gax.Grpc.pdb", "lib/netstandard2.0/Google.Api.Gax.Grpc.xml"]}, "Google.Api.Gax.Rest/4.8.0": {"sha512": "zaA5LZ2VvGj/wwIzRB68swr7khi2kWNgqWvsB0fYtScIAl3kGkGtqiBcx63H1YLeKr5xau1866bFjTeReH6FSQ==", "type": "package", "path": "google.api.gax.rest/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "NuGetIcon.png", "google.api.gax.rest.4.8.0.nupkg.sha512", "google.api.gax.rest.nuspec", "lib/net462/Google.Api.Gax.Rest.dll", "lib/net462/Google.Api.Gax.Rest.pdb", "lib/net462/Google.Api.Gax.Rest.xml", "lib/netstandard2.0/Google.Api.Gax.Rest.dll", "lib/netstandard2.0/Google.Api.Gax.Rest.pdb", "lib/netstandard2.0/Google.Api.Gax.Rest.xml"]}, "Google.Apis/1.68.0": {"sha512": "s2MymhdpH+ybZNBeZ2J5uFgFHApBp+QXf9FjZSdM1lk/vx5VqIknJwnaWiuAzXxPrLEkesX0Q+UsiWn39yZ9zw==", "type": "package", "path": "google.apis/1.68.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "NuGetIcon.png", "google.apis.1.68.0.nupkg.sha512", "google.apis.nuspec", "lib/net462/Google.Apis.dll", "lib/net462/Google.Apis.pdb", "lib/net462/Google.Apis.xml", "lib/net6.0/Google.Apis.dll", "lib/net6.0/Google.Apis.pdb", "lib/net6.0/Google.Apis.xml", "lib/netstandard2.0/Google.Apis.dll", "lib/netstandard2.0/Google.Apis.pdb", "lib/netstandard2.0/Google.Apis.xml"]}, "Google.Apis.Auth/1.68.0": {"sha512": "hFx8Qz5bZ4w0hpnn4tSmZaaFpjAMsgVElZ+ZgVLUZ2r9i+AKcoVgwiNfv1pruNS5cCvpXqhKECbruBCfRezPHA==", "type": "package", "path": "google.apis.auth/1.68.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "NuGetIcon.png", "google.apis.auth.1.68.0.nupkg.sha512", "google.apis.auth.nuspec", "lib/net462/Google.Apis.Auth.dll", "lib/net462/Google.Apis.Auth.pdb", "lib/net462/Google.Apis.Auth.xml", "lib/net6.0/Google.Apis.Auth.dll", "lib/net6.0/Google.Apis.Auth.pdb", "lib/net6.0/Google.Apis.Auth.xml", "lib/netstandard2.0/Google.Apis.Auth.dll", "lib/netstandard2.0/Google.Apis.Auth.pdb", "lib/netstandard2.0/Google.Apis.Auth.xml"]}, "Google.Apis.Core/1.68.0": {"sha512": "pAqwa6pfu53UXCR2b7A/PAPXeuVg6L1OFw38WckN27NU2+mf+KTjoEg2YGv/f0UyKxzz7DxF1urOTKg/6dTP9g==", "type": "package", "path": "google.apis.core/1.68.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "NuGetIcon.png", "google.apis.core.1.68.0.nupkg.sha512", "google.apis.core.nuspec", "lib/net462/Google.Apis.Core.dll", "lib/net462/Google.Apis.Core.pdb", "lib/net462/Google.Apis.Core.xml", "lib/net6.0/Google.Apis.Core.dll", "lib/net6.0/Google.Apis.Core.pdb", "lib/net6.0/Google.Apis.Core.xml", "lib/netstandard2.0/Google.Apis.Core.dll", "lib/netstandard2.0/Google.Apis.Core.pdb", "lib/netstandard2.0/Google.Apis.Core.xml"]}, "Google.Cloud.Firestore/3.10.0": {"sha512": "euC8WgTjtpV9mCAqNrg81Zs0ebW4kKcZwXTUCRnhLHHND2+DAe9q+z/SwsszK1kgTwDTKv0sW5O8qPriR+iw6w==", "type": "package", "path": "google.cloud.firestore/3.10.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "NuGetIcon.png", "google.cloud.firestore.3.10.0.nupkg.sha512", "google.cloud.firestore.nuspec", "lib/net462/Google.Cloud.Firestore.dll", "lib/net462/Google.Cloud.Firestore.pdb", "lib/net462/Google.Cloud.Firestore.xml", "lib/netstandard2.0/Google.Cloud.Firestore.dll", "lib/netstandard2.0/Google.Cloud.Firestore.pdb", "lib/netstandard2.0/Google.Cloud.Firestore.xml"]}, "Google.Cloud.Firestore.V1/3.10.0": {"sha512": "lyk4HmErTGYRqyP2hmPMZT0S39RnkRwqLsY4AHEeGefR4JbLcSN+4MzSOMEu18nCYcVN0yYIYVLmNE5dyIduHQ==", "type": "package", "path": "google.cloud.firestore.v1/3.10.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "NuGetIcon.png", "google.cloud.firestore.v1.3.10.0.nupkg.sha512", "google.cloud.firestore.v1.nuspec", "lib/net462/Google.Cloud.Firestore.V1.dll", "lib/net462/Google.Cloud.Firestore.V1.pdb", "lib/net462/Google.Cloud.Firestore.V1.xml", "lib/netstandard2.0/Google.Cloud.Firestore.V1.dll", "lib/netstandard2.0/Google.Cloud.Firestore.V1.pdb", "lib/netstandard2.0/Google.Cloud.Firestore.V1.xml"]}, "Google.Cloud.Location/2.3.0": {"sha512": "ABQ4EM7FsOM7tx0cmlkZmHFqH1LeCf4teWPM26UT7mZJzlH4Pk8HUcyi/xEFe3l6LanNFCTHbKT+eOlQ/axkJg==", "type": "package", "path": "google.cloud.location/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "NuGetIcon.png", "google.cloud.location.2.3.0.nupkg.sha512", "google.cloud.location.nuspec", "lib/net462/Google.Cloud.Location.dll", "lib/net462/Google.Cloud.Location.pdb", "lib/net462/Google.Cloud.Location.xml", "lib/netstandard2.0/Google.Cloud.Location.dll", "lib/netstandard2.0/Google.Cloud.Location.pdb", "lib/netstandard2.0/Google.Cloud.Location.xml"]}, "Google.LongRunning/3.3.0": {"sha512": "F2SZ83Jo466Wj/s1Z7QhIAmWBXxJZQyXZpcx0P8BR7d6s0FAj67vQjeUPESSJcvsy8AqYiYBhkUr2YpZhTQeHg==", "type": "package", "path": "google.longrunning/3.3.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "NuGetIcon.png", "google.longrunning.3.3.0.nupkg.sha512", "google.longrunning.nuspec", "lib/net462/Google.LongRunning.dll", "lib/net462/Google.LongRunning.pdb", "lib/net462/Google.LongRunning.xml", "lib/netstandard2.0/Google.LongRunning.dll", "lib/netstandard2.0/Google.LongRunning.pdb", "lib/netstandard2.0/Google.LongRunning.xml"]}, "Google.Protobuf/3.28.2": {"sha512": "Z86ZKAB+v1B/m0LTM+EVamvZlYw/g3VND3/Gs4M/+aDIxa2JE9YPKjDxTpf0gv2sh26hrve3eI03brxBmzn92g==", "type": "package", "path": "google.protobuf/3.28.2", "files": [".nupkg.metadata", ".signature.p7s", "google.protobuf.3.28.2.nupkg.sha512", "google.protobuf.nuspec", "lib/net45/Google.Protobuf.dll", "lib/net45/Google.Protobuf.pdb", "lib/net45/Google.Protobuf.xml", "lib/net5.0/Google.Protobuf.dll", "lib/net5.0/Google.Protobuf.pdb", "lib/net5.0/Google.Protobuf.xml", "lib/netstandard1.1/Google.Protobuf.dll", "lib/netstandard1.1/Google.Protobuf.pdb", "lib/netstandard1.1/Google.Protobuf.xml", "lib/netstandard2.0/Google.Protobuf.dll", "lib/netstandard2.0/Google.Protobuf.pdb", "lib/netstandard2.0/Google.Protobuf.xml"]}, "Grpc.Auth/2.66.0": {"sha512": "FRQlhMAcHf0GjAXIfhN6RydfZncLLXNNTOtpLL1bt57kp59vu40faW+dr6Vwl7ef/IUFfF38aiB5jvhAA/9Aow==", "type": "package", "path": "grpc.auth/2.66.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "grpc.auth.2.66.0.nupkg.sha512", "grpc.auth.nuspec", "lib/net462/Grpc.Auth.dll", "lib/net462/Grpc.Auth.pdb", "lib/net462/Grpc.Auth.xml", "lib/netstandard2.0/Grpc.Auth.dll", "lib/netstandard2.0/Grpc.Auth.pdb", "lib/netstandard2.0/Grpc.Auth.xml", "packageIcon.png"]}, "Grpc.Core.Api/2.66.0": {"sha512": "HsjsQVAHe4hqP4t4rpUnmq+MZvPdyrlPsWF4T5fbMvyP3o/lMV+KVJfDlaNH8+v0aGQTVT3EsDFufbhaWb52cw==", "type": "package", "path": "grpc.core.api/2.66.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "grpc.core.api.2.66.0.nupkg.sha512", "grpc.core.api.nuspec", "lib/net462/Grpc.Core.Api.dll", "lib/net462/Grpc.Core.Api.pdb", "lib/net462/Grpc.Core.Api.xml", "lib/netstandard2.0/Grpc.Core.Api.dll", "lib/netstandard2.0/Grpc.Core.Api.pdb", "lib/netstandard2.0/Grpc.Core.Api.xml", "lib/netstandard2.1/Grpc.Core.Api.dll", "lib/netstandard2.1/Grpc.Core.Api.pdb", "lib/netstandard2.1/Grpc.Core.Api.xml", "packageIcon.png"]}, "Grpc.Net.Client/2.66.0": {"sha512": "GwkSsssXFgN9+M2U+UQWdErf61sn1iqgP+2NRBlDXATcP9vlxda0wySxd/eIL8U522+SnyFNUXlvQ5tAzGk9cA==", "type": "package", "path": "grpc.net.client/2.66.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "grpc.net.client.2.66.0.nupkg.sha512", "grpc.net.client.nuspec", "lib/net462/Grpc.Net.Client.dll", "lib/net462/Grpc.Net.Client.pdb", "lib/net462/Grpc.Net.Client.xml", "lib/net6.0/Grpc.Net.Client.dll", "lib/net6.0/Grpc.Net.Client.pdb", "lib/net6.0/Grpc.Net.Client.xml", "lib/net7.0/Grpc.Net.Client.dll", "lib/net7.0/Grpc.Net.Client.pdb", "lib/net7.0/Grpc.Net.Client.xml", "lib/net8.0/Grpc.Net.Client.dll", "lib/net8.0/Grpc.Net.Client.pdb", "lib/net8.0/Grpc.Net.Client.xml", "lib/netstandard2.0/Grpc.Net.Client.dll", "lib/netstandard2.0/Grpc.Net.Client.pdb", "lib/netstandard2.0/Grpc.Net.Client.xml", "lib/netstandard2.1/Grpc.Net.Client.dll", "lib/netstandard2.1/Grpc.Net.Client.pdb", "lib/netstandard2.1/Grpc.Net.Client.xml", "packageIcon.png"]}, "Grpc.Net.Common/2.66.0": {"sha512": "YJpQpIvpo0HKlsG6SHwaieyji08qfv0DdEDIewCAA0egQY08637sHOj1netLGUhzBEsCqlGC3e92TZ2uqhxnvw==", "type": "package", "path": "grpc.net.common/2.66.0", "files": [".nupkg.metadata", ".signature.p7s", "grpc.net.common.2.66.0.nupkg.sha512", "grpc.net.common.nuspec", "lib/net6.0/Grpc.Net.Common.dll", "lib/net6.0/Grpc.Net.Common.pdb", "lib/net6.0/Grpc.Net.Common.xml", "lib/net7.0/Grpc.Net.Common.dll", "lib/net7.0/Grpc.Net.Common.pdb", "lib/net7.0/Grpc.Net.Common.xml", "lib/net8.0/Grpc.Net.Common.dll", "lib/net8.0/Grpc.Net.Common.pdb", "lib/net8.0/Grpc.Net.Common.xml", "lib/netstandard2.0/Grpc.Net.Common.dll", "lib/netstandard2.0/Grpc.Net.Common.pdb", "lib/netstandard2.0/Grpc.Net.Common.xml", "lib/netstandard2.1/Grpc.Net.Common.dll", "lib/netstandard2.1/Grpc.Net.Common.pdb", "lib/netstandard2.1/Grpc.Net.Common.xml", "packageIcon.png"]}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"sha512": "UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"sha512": "xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"sha512": "/HggWBbTwy8TgebGSX5DBZ24ndhzi93sHUBDvP1IxbZD7FDokYzdAr6+vbWGjw2XAfR2EJ1sfKUotpjHnFWPxA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "build/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net461/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.VisualBasic/10.3.0": {"sha512": "AvMDjmJHjz9bdlvxiSdEHHcWP+sZtp7zwule5ab6DaUbgoBnwCsd7nymj69vSz18ypXuEv3SI7ZUNwbIKrvtMA==", "type": "package", "path": "microsoft.visualbasic/10.3.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/_._", "lib/netcore50/Microsoft.VisualBasic.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.VisualBasic.dll", "lib/netstandard2.0/Microsoft.VisualBasic.dll", "lib/portable-net45+win8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wpa81/_._", "microsoft.visualbasic.10.3.0.nupkg.sha512", "microsoft.visualbasic.nuspec", "ref/net45/_._", "ref/netcore50/Microsoft.VisualBasic.dll", "ref/netcore50/Microsoft.VisualBasic.xml", "ref/netcore50/de/Microsoft.VisualBasic.xml", "ref/netcore50/es/Microsoft.VisualBasic.xml", "ref/netcore50/fr/Microsoft.VisualBasic.xml", "ref/netcore50/it/Microsoft.VisualBasic.xml", "ref/netcore50/ja/Microsoft.VisualBasic.xml", "ref/netcore50/ko/Microsoft.VisualBasic.xml", "ref/netcore50/ru/Microsoft.VisualBasic.xml", "ref/netcore50/zh-hans/Microsoft.VisualBasic.xml", "ref/netcore50/zh-hant/Microsoft.VisualBasic.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/Microsoft.VisualBasic.dll", "ref/netstandard1.1/Microsoft.VisualBasic.xml", "ref/netstandard1.1/de/Microsoft.VisualBasic.xml", "ref/netstandard1.1/es/Microsoft.VisualBasic.xml", "ref/netstandard1.1/fr/Microsoft.VisualBasic.xml", "ref/netstandard1.1/it/Microsoft.VisualBasic.xml", "ref/netstandard1.1/ja/Microsoft.VisualBasic.xml", "ref/netstandard1.1/ko/Microsoft.VisualBasic.xml", "ref/netstandard1.1/ru/Microsoft.VisualBasic.xml", "ref/netstandard1.1/zh-hans/Microsoft.VisualBasic.xml", "ref/netstandard1.1/zh-hant/Microsoft.VisualBasic.xml", "ref/netstandard2.0/Microsoft.VisualBasic.dll", "ref/netstandard2.0/Microsoft.VisualBasic.xml", "ref/portable-net45+win8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wpa81/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Serilog/4.3.0": {"sha512": "+cDryFR0GRhsGOnZSKwaDzRRl4MupvJ42FhCE4zhQRVanX0Jpg6WuCBk59OVhVDPmab1bB+nRykAnykYELA9qQ==", "type": "package", "path": "serilog/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "build/Serilog.targets", "icon.png", "lib/net462/Serilog.dll", "lib/net462/Serilog.xml", "lib/net471/Serilog.dll", "lib/net471/Serilog.xml", "lib/net6.0/Serilog.dll", "lib/net6.0/Serilog.xml", "lib/net8.0/Serilog.dll", "lib/net8.0/Serilog.xml", "lib/net9.0/Serilog.dll", "lib/net9.0/Serilog.xml", "lib/netstandard2.0/Serilog.dll", "lib/netstandard2.0/Serilog.xml", "serilog.4.3.0.nupkg.sha512", "serilog.nuspec"]}, "Serilog.Sinks.Console/6.0.0": {"sha512": "fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "type": "package", "path": "serilog.sinks.console/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Sinks.Console.dll", "lib/net462/Serilog.Sinks.Console.xml", "lib/net471/Serilog.Sinks.Console.dll", "lib/net471/Serilog.Sinks.Console.xml", "lib/net6.0/Serilog.Sinks.Console.dll", "lib/net6.0/Serilog.Sinks.Console.xml", "lib/net8.0/Serilog.Sinks.Console.dll", "lib/net8.0/Serilog.Sinks.Console.xml", "lib/netstandard2.0/Serilog.Sinks.Console.dll", "lib/netstandard2.0/Serilog.Sinks.Console.xml", "serilog.sinks.console.6.0.0.nupkg.sha512", "serilog.sinks.console.nuspec"]}, "Serilog.Sinks.File/7.0.0": {"sha512": "fKL7mXv7qaiNBUC71ssvn/dU0k9t0o45+qm2XgKAlSt19xF+ijjxyA3R6HmCgfKEKwfcfkwWjayuQtRueZFkYw==", "type": "package", "path": "serilog.sinks.file/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Sinks.File.dll", "lib/net462/Serilog.Sinks.File.xml", "lib/net471/Serilog.Sinks.File.dll", "lib/net471/Serilog.Sinks.File.xml", "lib/net6.0/Serilog.Sinks.File.dll", "lib/net6.0/Serilog.Sinks.File.xml", "lib/net8.0/Serilog.Sinks.File.dll", "lib/net8.0/Serilog.Sinks.File.xml", "lib/net9.0/Serilog.Sinks.File.dll", "lib/net9.0/Serilog.Sinks.File.xml", "lib/netstandard2.0/Serilog.Sinks.File.dll", "lib/netstandard2.0/Serilog.Sinks.File.xml", "serilog-sink-nuget.png", "serilog.sinks.file.7.0.0.nupkg.sha512", "serilog.sinks.file.nuspec"]}, "System.CodeDom/7.0.0": {"sha512": "GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "type": "package", "path": "system.codedom/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/net7.0/System.CodeDom.dll", "lib/net7.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.7.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Collections.Immutable/8.0.0": {"sha512": "AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "type": "package", "path": "system.collections.immutable/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Collections.Immutable.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Collections.Immutable.targets", "lib/net462/System.Collections.Immutable.dll", "lib/net462/System.Collections.Immutable.xml", "lib/net6.0/System.Collections.Immutable.dll", "lib/net6.0/System.Collections.Immutable.xml", "lib/net7.0/System.Collections.Immutable.dll", "lib/net7.0/System.Collections.Immutable.xml", "lib/net8.0/System.Collections.Immutable.dll", "lib/net8.0/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "system.collections.immutable.8.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.DiagnosticSource/6.0.1": {"sha512": "KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Diagnostics.DiagnosticSource.dll", "lib/net461/System.Diagnostics.DiagnosticSource.xml", "lib/net5.0/System.Diagnostics.DiagnosticSource.dll", "lib/net5.0/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Linq.Async/6.0.1": {"sha512": "0YhHcaroWpQ9UCot3Pizah7ryAzQhNvobLMSxeDIGmnXfkQn8u5owvpOH0K6EVB+z9L7u6Cc4W17Br/+jyttEQ==", "type": "package", "path": "system.linq.async/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Logo.png", "lib/net48/System.Linq.Async.dll", "lib/net48/System.Linq.Async.xml", "lib/net6.0/System.Linq.Async.dll", "lib/net6.0/System.Linq.Async.xml", "lib/netstandard2.0/System.Linq.Async.dll", "lib/netstandard2.0/System.Linq.Async.xml", "lib/netstandard2.1/System.Linq.Async.dll", "lib/netstandard2.1/System.Linq.Async.xml", "ref/net48/System.Linq.Async.dll", "ref/net48/System.Linq.Async.xml", "ref/net6.0/System.Linq.Async.dll", "ref/net6.0/System.Linq.Async.xml", "ref/netstandard2.0/System.Linq.Async.dll", "ref/netstandard2.0/System.Linq.Async.xml", "ref/netstandard2.1/System.Linq.Async.dll", "ref/netstandard2.1/System.Linq.Async.xml", "system.linq.async.6.0.1.nupkg.sha512", "system.linq.async.nuspec"]}, "System.Management/7.0.2": {"sha512": "/qEUN91mP/MUQmJnM5y5BdT7ZoPuVrtxnFlbJ8a3kBJGhe2wCzBfnPFtK2wTtEEcf3DMGR9J00GZZfg6HRI6yA==", "type": "package", "path": "system.management/7.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net6.0/System.Management.dll", "lib/net6.0/System.Management.xml", "lib/net7.0/System.Management.dll", "lib/net7.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net6.0/System.Management.dll", "runtimes/win/lib/net6.0/System.Management.xml", "runtimes/win/lib/net7.0/System.Management.dll", "runtimes/win/lib/net7.0/System.Management.xml", "system.management.7.0.2.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["CommunityToolkit.Mvvm >= 8.4.0", "FirebaseAdmin >= 3.2.0", "Google.Cloud.Firestore >= 3.10.0", "Microsoft.VisualBasic >= 10.3.0", "Serilog >= 4.3.0", "Serilog.Sinks.Console >= 6.0.0", "Serilog.Sinks.File >= 7.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\PycharmProjects\\Apartman_Yonetim_Sistemi\\ApartmanYonetimSistemi\\ApartmanYonetimSistemi.csproj", "projectName": "ApartmanYonetimSistemi", "projectPath": "C:\\Users\\<USER>\\Desktop\\PycharmProjects\\Apartman_Yonetim_Sistemi\\ApartmanYonetimSistemi\\ApartmanYonetimSistemi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\PycharmProjects\\Apartman_Yonetim_Sistemi\\ApartmanYonetimSistemi\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "FirebaseAdmin": {"target": "Package", "version": "[3.2.0, )"}, "Google.Cloud.Firestore": {"target": "Package", "version": "[3.10.0, )"}, "Microsoft.VisualBasic": {"target": "Package", "version": "[10.3.0, )"}, "Serilog": {"target": "Package", "version": "[4.3.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}