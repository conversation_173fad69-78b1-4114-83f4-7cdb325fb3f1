using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using Serilog;
using System;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class ApartmentDetailViewModel : ObservableObject
    {
        private readonly FirebaseService _firebaseService;

        [ObservableProperty]
        private Apartment? currentApartment;

        [ObservableProperty]
        private ObservableCollection<Daire> daireler = new();

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private Daire? selectedDaire;

        public ApartmentDetailViewModel()
        {
            _firebaseService = FirebaseService.Instance;
        }

        public void Initialize(Apartment apartment)
        {
            CurrentApartment = apartment;
            _ = LoadDairelerAsync();
        }

        [RelayCommand]
        private async Task LoadDairelerAsync()
        {
            if (CurrentApartment == null) return;

            IsLoading = true;
            try
            {
                Daireler.Clear();

                var dairelerCollection = _firebaseService.FirestoreDb
                    .Collection("Sites")
                    .Document(CurrentApartment.SiteId)
                    .Collection("Apartments")
                    .Document(CurrentApartment.Id)
                    .Collection("Daireler");

                var snapshot = await dairelerCollection.GetSnapshotAsync();

                foreach (var document in snapshot.Documents)
                {
                    var daire = document.ConvertTo<Daire>();
                    daire.Id = document.Id;
                    daire.ApartmentId = CurrentApartment.Id;
                    Daireler.Add(daire);
                }

                Log.Information("Apartman daireleri yüklendi: {Count} daire", Daireler.Count);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Daireler yüklenirken hata oluştu");
                MessageBox.Show($"Daireler yüklenirken hata oluştu: {ex.Message}", "Hata", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void SelectDaire(Daire daire)
        {
            SelectedDaire = daire;
            Log.Information("Daire seçildi: {DaireNo}", daire.DaireNo);

            // Daire detay ekranını aç
            var daireDetailWindow = new Views.DaireDetailWindow();
            if (daireDetailWindow.DataContext is DaireDetailViewModel vm)
            {
                vm.Initialize(daire);
            }
            daireDetailWindow.Show();
        }

        [RelayCommand]
        private async Task CreateNewDaireAsync()
        {
            if (CurrentApartment == null) return;

            var daireNo = Microsoft.VisualBasic.Interaction.InputBox(
                "Daire numarasını girin:", "Yeni Daire", "");

            if (!string.IsNullOrWhiteSpace(daireNo))
            {
                await CreateDaireAsync(daireNo);
            }
        }

        private async Task CreateDaireAsync(string daireNo)
        {
            if (CurrentApartment == null) return;

            IsLoading = true;
            try
            {
                var newDaire = new Daire
                {
                    Id = Guid.NewGuid().ToString(),
                    DaireNo = daireNo,
                    ApartmentId = CurrentApartment.Id,
                    KiraBedeli = 0,
                    Aidat = 0,
                    KiraDurumu = PaymentStatus.Borclu,
                    AidatDurumu = PaymentStatus.Borclu
                };

                await _firebaseService.FirestoreDb
                    .Collection("Sites")
                    .Document(CurrentApartment.SiteId)
                    .Collection("Apartments")
                    .Document(CurrentApartment.Id)
                    .Collection("Daireler")
                    .Document(newDaire.Id)
                    .SetAsync(newDaire);

                Daireler.Add(newDaire);
                Log.Information("Yeni daire oluşturuldu: {DaireNo}", daireNo);
                
                MessageBox.Show($"'{daireNo}' numaralı daire başarıyla oluşturuldu!", "Başarılı", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Daire oluşturulurken hata oluştu");
                MessageBox.Show($"Daire oluşturulurken hata oluştu: {ex.Message}", "Hata", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void GoBack()
        {
            if (CurrentApartment == null) return;

            // Site detay ekranına dön
            var siteDetailWindow = new Views.SiteDetailWindow();
            if (siteDetailWindow.DataContext is SiteDetailViewModel vm)
            {
                // Site bilgisini almak için Firestore'dan çek
                _ = LoadSiteAndInitialize(vm);
            }
            siteDetailWindow.Show();
            
            // Mevcut pencereyi kapat
            foreach (Window window in Application.Current.Windows)
            {
                if (window.GetType().Name == "ApartmentDetailWindow")
                {
                    window.Close();
                    break;
                }
            }
        }

        private async Task LoadSiteAndInitialize(SiteDetailViewModel vm)
        {
            try
            {
                var siteDoc = await _firebaseService.FirestoreDb
                    .Collection("Sites")
                    .Document(CurrentApartment!.SiteId)
                    .GetSnapshotAsync();

                if (siteDoc.Exists)
                {
                    var site = siteDoc.ConvertTo<Site>();
                    site.Id = siteDoc.Id;
                    vm.Initialize(site);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Site bilgisi yüklenirken hata oluştu");
            }
        }
    }
}
