using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using Serilog;
using System;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class DashboardViewModel : ObservableObject
    {
        private readonly AuthenticationService _authService;
        private readonly FirebaseService _firebaseService;

        [ObservableProperty]
        private User? currentUser;

        [ObservableProperty]
        private ObservableCollection<Site> userSites = new();

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string welcomeMessage = string.Empty;

        [ObservableProperty]
        private Site? selectedSite;

        [ObservableProperty]
        private int totalSites = 0;

        [ObservableProperty]
        private int totalApartments = 0;

        [ObservableProperty]
        private int totalDaireler = 0;

        [ObservableProperty]
        private int occupiedDaireler = 0;

        [ObservableProperty]
        private decimal monthlyIncome = 0;

        [ObservableProperty]
        private int borcluDaireler = 0;

        public DashboardViewModel()
        {
            _authService = new AuthenticationService();
            _firebaseService = FirebaseService.Instance;
            
            // Kullanıcı değişikliklerini dinle
            _authService.UserChanged += OnUserChanged;
            
            // Mevcut kullanıcıyı al
            CurrentUser = _authService.CurrentUser;
            UpdateWelcomeMessage();
            
            // Kullanıcının sitelerini yükle
            _ = LoadUserSitesAsync();

            // İstatistikleri yükle
            _ = LoadStatisticsAsync();
        }

        private void OnUserChanged(object? sender, User? user)
        {
            CurrentUser = user;
            UpdateWelcomeMessage();
            _ = LoadUserSitesAsync();
            _ = LoadStatisticsAsync();
        }

        private void UpdateWelcomeMessage()
        {
            if (CurrentUser != null)
            {
                var timeOfDay = DateTime.Now.Hour switch
                {
                    < 12 => "Günaydın",
                    < 18 => "İyi günler",
                    _ => "İyi akşamlar"
                };
                WelcomeMessage = $"{timeOfDay}, {CurrentUser.Name}!";
            }
            else
            {
                WelcomeMessage = "Hoş geldiniz!";
            }
        }

        [RelayCommand]
        private async Task LoadUserSitesAsync()
        {
            if (CurrentUser == null) return;

            IsLoading = true;
            try
            {
                UserSites.Clear();

                // Admin ise tüm siteleri getir, değilse sadece atanmış siteleri
                if (CurrentUser.Role == UserRole.Admin)
                {
                    var sitesCollection = _firebaseService.FirestoreDb.Collection("Sites");
                    var snapshot = await sitesCollection.GetSnapshotAsync();

                    foreach (var document in snapshot.Documents)
                    {
                        var site = document.ConvertTo<Site>();
                        site.Id = document.Id;
                        UserSites.Add(site);
                    }
                }
                else
                {
                    // Kullanıcının atanmış sitelerini getir
                    foreach (var siteId in CurrentUser.AssignedSites)
                    {
                        var siteDoc = await _firebaseService.FirestoreDb
                            .Collection("Sites")
                            .Document(siteId)
                            .GetSnapshotAsync();

                        if (siteDoc.Exists)
                        {
                            var site = siteDoc.ConvertTo<Site>();
                            site.Id = siteDoc.Id;
                            UserSites.Add(site);
                        }
                    }
                }

                Log.Information("Kullanıcı siteleri yüklendi: {Count} site", UserSites.Count);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Siteler yüklenirken hata oluştu");
                MessageBox.Show($"Siteler yüklenirken hata oluştu: {ex.Message}", "Hata", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void SelectSite(Site site)
        {
            SelectedSite = site;
            Log.Information("Site seçildi: {SiteName}", site.SiteName);

            // Site detay ekranını aç
            var siteDetailWindow = new Views.SiteDetailWindow();
            if (siteDetailWindow.DataContext is SiteDetailViewModel vm)
            {
                vm.Initialize(site);
            }
            siteDetailWindow.Show();

            // Mevcut pencereyi kapat
            Application.Current.MainWindow?.Close();
        }

        [RelayCommand]
        private async Task CreateNewSiteAsync()
        {
            // Yeni site oluşturma ekranına geçiş
            Log.Information("Yeni site oluşturma işlemi başlatıldı");
            
            // Şimdilik basit bir dialog
            var siteName = Microsoft.VisualBasic.Interaction.InputBox(
                "Site adını girin:", "Yeni Site", "");

            if (!string.IsNullOrWhiteSpace(siteName))
            {
                await CreateSiteAsync(siteName);
            }
        }

        private async Task CreateSiteAsync(string siteName)
        {
            IsLoading = true;
            try
            {
                var newSite = new Site
                {
                    Id = Guid.NewGuid().ToString(),
                    SiteName = siteName,
                    Address = "",
                    Managers = new() { CurrentUser?.Id ?? "" }
                };

                await _firebaseService.FirestoreDb
                    .Collection("Sites")
                    .Document(newSite.Id)
                    .SetAsync(newSite);

                UserSites.Add(newSite);
                Log.Information("Yeni site oluşturuldu: {SiteName}", siteName);
                
                MessageBox.Show($"'{siteName}' sitesi başarıyla oluşturuldu!", "Başarılı", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Site oluşturulurken hata oluştu");
                MessageBox.Show($"Site oluşturulurken hata oluştu: {ex.Message}", "Hata", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void Logout()
        {
            _authService.Logout();

            // Login ekranına dön
            var loginWindow = new Views.LoginWindow();
            loginWindow.Show();

            // Mevcut pencereyi kapat
            Application.Current.MainWindow?.Close();
        }

        [RelayCommand]
        private async Task LoadStatisticsAsync()
        {
            if (CurrentUser == null) return;

            try
            {
                TotalSites = UserSites.Count;
                TotalApartments = 0;
                TotalDaireler = 0;
                OccupiedDaireler = 0;
                MonthlyIncome = 0;
                BorcluDaireler = 0;

                foreach (var site in UserSites)
                {
                    // Apartmanları say
                    var apartmentsCollection = _firebaseService.FirestoreDb
                        .Collection("Sites")
                        .Document(site.Id)
                        .Collection("Apartments");

                    var apartmentsSnapshot = await apartmentsCollection.GetSnapshotAsync();
                    TotalApartments += apartmentsSnapshot.Documents.Count;

                    // Her apartman için daireleri say
                    foreach (var apartmentDoc in apartmentsSnapshot.Documents)
                    {
                        var dairelerCollection = apartmentsCollection
                            .Document(apartmentDoc.Id)
                            .Collection("Daireler");

                        var dairelerSnapshot = await dairelerCollection.GetSnapshotAsync();
                        TotalDaireler += dairelerSnapshot.Documents.Count;

                        // Daire detaylarını kontrol et
                        foreach (var daireDoc in dairelerSnapshot.Documents)
                        {
                            var daire = daireDoc.ConvertTo<Daire>();

                            // Dolu daireleri say
                            if (daire.Kiraci != null)
                                OccupiedDaireler++;

                            // Bu ay ödenen kira ve aidatları topla
                            var thisMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                            var paymentsCollection = dairelerCollection
                                .Document(daireDoc.Id)
                                .Collection("Payments");

                            var paymentsQuery = paymentsCollection
                                .WhereGreaterThanOrEqualTo("Tarih", thisMonth)
                                .WhereEqualTo("OdemeDurumu", PaymentStatus.Odendi);

                            var paymentsSnapshot = await paymentsQuery.GetSnapshotAsync();

                            foreach (var paymentDoc in paymentsSnapshot.Documents)
                            {
                                var payment = paymentDoc.ConvertTo<Payment>();
                                MonthlyIncome += payment.Tutar;
                            }

                            // Borçlu daireleri say
                            if (daire.KiraDurumu == PaymentStatus.Borclu ||
                                daire.AidatDurumu == PaymentStatus.Borclu)
                            {
                                BorcluDaireler++;
                            }
                        }
                    }
                }

                Log.Information("Dashboard istatistikleri yüklendi");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "İstatistikler yüklenirken hata oluştu");
            }
        }

        [RelayCommand]
        private void OpenReports()
        {
            var reportsWindow = new Views.ReportsWindow();
            reportsWindow.Show();
        }
    }
}
