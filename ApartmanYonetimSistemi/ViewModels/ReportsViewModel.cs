using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using Serilog;
using System;
using System.Linq;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class ReportsViewModel : ObservableObject
    {
        private readonly FirebaseService _firebaseService;
        private readonly AuthenticationService _authService;

        [ObservableProperty]
        private ObservableCollection<MonthlyIncomeReport> monthlyReports = new();

        [ObservableProperty]
        private ObservableCollection<DaireWithDetails> borcluDaireler = new();

        [ObservableProperty]
        private ObservableCollection<Payment> recentPayments = new();

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private DateTime selectedMonth = DateTime.Now;

        [ObservableProperty]
        private decimal totalMonthlyIncome = 0;

        [ObservableProperty]
        private decimal totalYearlyIncome = 0;

        public ReportsViewModel()
        {
            _firebaseService = FirebaseService.Instance;
            _authService = new AuthenticationService();
            
            _ = LoadReportsAsync();
        }

        [RelayCommand]
        private async Task LoadReportsAsync()
        {
            IsLoading = true;
            try
            {
                await LoadMonthlyReportsAsync();
                await LoadBorcluDairelerAsync();
                await LoadRecentPaymentsAsync();
                
                Log.Information("Raporlar yüklendi");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Raporlar yüklenirken hata oluştu");
                MessageBox.Show($"Raporlar yüklenirken hata oluştu: {ex.Message}", "Hata", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadMonthlyReportsAsync()
        {
            MonthlyReports.Clear();
            TotalYearlyIncome = 0;

            // Son 12 ay için rapor oluştur
            for (int i = 11; i >= 0; i--)
            {
                var month = DateTime.Now.AddMonths(-i);
                var monthStart = new DateTime(month.Year, month.Month, 1);
                var monthEnd = monthStart.AddMonths(1).AddDays(-1);

                var monthlyIncome = await CalculateMonthlyIncomeAsync(monthStart, monthEnd);
                
                var report = new MonthlyIncomeReport
                {
                    Month = monthStart,
                    MonthName = monthStart.ToString("MMMM yyyy"),
                    TotalIncome = monthlyIncome,
                    KiraIncome = await CalculateIncomeByTypeAsync(monthStart, monthEnd, PaymentType.Kira),
                    AidatIncome = await CalculateIncomeByTypeAsync(monthStart, monthEnd, PaymentType.Aidat)
                };

                MonthlyReports.Add(report);
                TotalYearlyIncome += monthlyIncome;
            }

            // Seçili ay geliri
            var selectedMonthStart = new DateTime(SelectedMonth.Year, SelectedMonth.Month, 1);
            var selectedMonthEnd = selectedMonthStart.AddMonths(1).AddDays(-1);
            TotalMonthlyIncome = await CalculateMonthlyIncomeAsync(selectedMonthStart, selectedMonthEnd);
        }

        private async Task LoadBorcluDairelerAsync()
        {
            BorcluDaireler.Clear();

            var currentUser = _authService.CurrentUser;
            if (currentUser == null) return;

            // Kullanıcının sitelerini al
            var sitesQuery = currentUser.Role == UserRole.Admin 
                ? _firebaseService.FirestoreDb.Collection("Sites")
                : _firebaseService.FirestoreDb.Collection("Sites").WhereIn("Id", currentUser.AssignedSites);

            var sitesSnapshot = await sitesQuery.GetSnapshotAsync();

            foreach (var siteDoc in sitesSnapshot.Documents)
            {
                var site = siteDoc.ConvertTo<Site>();
                site.Id = siteDoc.Id;

                var apartmentsSnapshot = await _firebaseService.FirestoreDb
                    .Collection("Sites")
                    .Document(site.Id)
                    .Collection("Apartments")
                    .GetSnapshotAsync();

                foreach (var apartmentDoc in apartmentsSnapshot.Documents)
                {
                    var apartment = apartmentDoc.ConvertTo<Apartment>();
                    apartment.Id = apartmentDoc.Id;

                    var dairelerSnapshot = await _firebaseService.FirestoreDb
                        .Collection("Sites")
                        .Document(site.Id)
                        .Collection("Apartments")
                        .Document(apartment.Id)
                        .Collection("Daireler")
                        .GetSnapshotAsync();

                    foreach (var daireDoc in dairelerSnapshot.Documents)
                    {
                        var daire = daireDoc.ConvertTo<Daire>();
                        daire.Id = daireDoc.Id;

                        if (daire.KiraDurumu == PaymentStatus.Borclu || 
                            daire.AidatDurumu == PaymentStatus.Borclu)
                        {
                            var daireWithDetails = new DaireWithDetails
                            {
                                Daire = daire,
                                SiteName = site.SiteName,
                                ApartmentName = apartment.ApartmentName,
                                TotalDebt = 0
                            };

                            // Borç tutarını hesapla
                            if (daire.KiraDurumu == PaymentStatus.Borclu)
                                daireWithDetails.TotalDebt += daire.KiraBedeli;
                            if (daire.AidatDurumu == PaymentStatus.Borclu)
                                daireWithDetails.TotalDebt += daire.Aidat;

                            BorcluDaireler.Add(daireWithDetails);
                        }
                    }
                }
            }
        }

        private async Task LoadRecentPaymentsAsync()
        {
            RecentPayments.Clear();

            var currentUser = _authService.CurrentUser;
            if (currentUser == null) return;

            var recentPaymentsList = new List<Payment>();

            // Son 30 günün ödemelerini al
            var thirtyDaysAgo = DateTime.Now.AddDays(-30);

            var sitesQuery = currentUser.Role == UserRole.Admin 
                ? _firebaseService.FirestoreDb.Collection("Sites")
                : _firebaseService.FirestoreDb.Collection("Sites").WhereIn("Id", currentUser.AssignedSites);

            var sitesSnapshot = await sitesQuery.GetSnapshotAsync();

            foreach (var siteDoc in sitesSnapshot.Documents)
            {
                var apartmentsSnapshot = await _firebaseService.FirestoreDb
                    .Collection("Sites")
                    .Document(siteDoc.Id)
                    .Collection("Apartments")
                    .GetSnapshotAsync();

                foreach (var apartmentDoc in apartmentsSnapshot.Documents)
                {
                    var dairelerSnapshot = await _firebaseService.FirestoreDb
                        .Collection("Sites")
                        .Document(siteDoc.Id)
                        .Collection("Apartments")
                        .Document(apartmentDoc.Id)
                        .Collection("Daireler")
                        .GetSnapshotAsync();

                    foreach (var daireDoc in dairelerSnapshot.Documents)
                    {
                        var paymentsSnapshot = await _firebaseService.FirestoreDb
                            .Collection("Sites")
                            .Document(siteDoc.Id)
                            .Collection("Apartments")
                            .Document(apartmentDoc.Id)
                            .Collection("Daireler")
                            .Document(daireDoc.Id)
                            .Collection("Payments")
                            .WhereGreaterThanOrEqualTo("Tarih", thirtyDaysAgo)
                            .OrderByDescending("Tarih")
                            .GetSnapshotAsync();

                        foreach (var paymentDoc in paymentsSnapshot.Documents)
                        {
                            var payment = paymentDoc.ConvertTo<Payment>();
                            payment.Id = paymentDoc.Id;
                            recentPaymentsList.Add(payment);
                        }
                    }
                }
            }

            // Son ödemeleri tarihe göre sırala ve ilk 50'sini al
            var sortedPayments = recentPaymentsList
                .OrderByDescending(p => p.Tarih)
                .Take(50);

            foreach (var payment in sortedPayments)
            {
                RecentPayments.Add(payment);
            }
        }

        private async Task<decimal> CalculateMonthlyIncomeAsync(DateTime monthStart, DateTime monthEnd)
        {
            decimal totalIncome = 0;
            var currentUser = _authService.CurrentUser;
            if (currentUser == null) return totalIncome;

            var sitesQuery = currentUser.Role == UserRole.Admin 
                ? _firebaseService.FirestoreDb.Collection("Sites")
                : _firebaseService.FirestoreDb.Collection("Sites").WhereIn("Id", currentUser.AssignedSites);

            var sitesSnapshot = await sitesQuery.GetSnapshotAsync();

            foreach (var siteDoc in sitesSnapshot.Documents)
            {
                var apartmentsSnapshot = await _firebaseService.FirestoreDb
                    .Collection("Sites")
                    .Document(siteDoc.Id)
                    .Collection("Apartments")
                    .GetSnapshotAsync();

                foreach (var apartmentDoc in apartmentsSnapshot.Documents)
                {
                    var dairelerSnapshot = await _firebaseService.FirestoreDb
                        .Collection("Sites")
                        .Document(siteDoc.Id)
                        .Collection("Apartments")
                        .Document(apartmentDoc.Id)
                        .Collection("Daireler")
                        .GetSnapshotAsync();

                    foreach (var daireDoc in dairelerSnapshot.Documents)
                    {
                        var paymentsSnapshot = await _firebaseService.FirestoreDb
                            .Collection("Sites")
                            .Document(siteDoc.Id)
                            .Collection("Apartments")
                            .Document(apartmentDoc.Id)
                            .Collection("Daireler")
                            .Document(daireDoc.Id)
                            .Collection("Payments")
                            .WhereGreaterThanOrEqualTo("Tarih", monthStart)
                            .WhereLessThanOrEqualTo("Tarih", monthEnd)
                            .WhereEqualTo("OdemeDurumu", PaymentStatus.Odendi)
                            .GetSnapshotAsync();

                        foreach (var paymentDoc in paymentsSnapshot.Documents)
                        {
                            var payment = paymentDoc.ConvertTo<Payment>();
                            totalIncome += payment.Tutar;
                        }
                    }
                }
            }

            return totalIncome;
        }

        private async Task<decimal> CalculateIncomeByTypeAsync(DateTime monthStart, DateTime monthEnd, PaymentType type)
        {
            decimal income = 0;
            var currentUser = _authService.CurrentUser;
            if (currentUser == null) return income;

            var sitesQuery = currentUser.Role == UserRole.Admin 
                ? _firebaseService.FirestoreDb.Collection("Sites")
                : _firebaseService.FirestoreDb.Collection("Sites").WhereIn("Id", currentUser.AssignedSites);

            var sitesSnapshot = await sitesQuery.GetSnapshotAsync();

            foreach (var siteDoc in sitesSnapshot.Documents)
            {
                var apartmentsSnapshot = await _firebaseService.FirestoreDb
                    .Collection("Sites")
                    .Document(siteDoc.Id)
                    .Collection("Apartments")
                    .GetSnapshotAsync();

                foreach (var apartmentDoc in apartmentsSnapshot.Documents)
                {
                    var dairelerSnapshot = await _firebaseService.FirestoreDb
                        .Collection("Sites")
                        .Document(siteDoc.Id)
                        .Collection("Apartments")
                        .Document(apartmentDoc.Id)
                        .Collection("Daireler")
                        .GetSnapshotAsync();

                    foreach (var daireDoc in dairelerSnapshot.Documents)
                    {
                        var paymentsSnapshot = await _firebaseService.FirestoreDb
                            .Collection("Sites")
                            .Document(siteDoc.Id)
                            .Collection("Apartments")
                            .Document(apartmentDoc.Id)
                            .Collection("Daireler")
                            .Document(daireDoc.Id)
                            .Collection("Payments")
                            .WhereGreaterThanOrEqualTo("Tarih", monthStart)
                            .WhereLessThanOrEqualTo("Tarih", monthEnd)
                            .WhereEqualTo("OdemeDurumu", PaymentStatus.Odendi)
                            .WhereEqualTo("Tip", type)
                            .GetSnapshotAsync();

                        foreach (var paymentDoc in paymentsSnapshot.Documents)
                        {
                            var payment = paymentDoc.ConvertTo<Payment>();
                            income += payment.Tutar;
                        }
                    }
                }
            }

            return income;
        }

        [RelayCommand]
        private async Task RefreshReportsAsync()
        {
            await LoadReportsAsync();
        }

        [RelayCommand]
        private void ExportToExcel()
        {
            // Excel export özelliği - şimdilik basit mesaj
            MessageBox.Show("Excel export özelliği yakında eklenecek!", "Bilgi", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    public class MonthlyIncomeReport
    {
        public DateTime Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public decimal TotalIncome { get; set; }
        public decimal KiraIncome { get; set; }
        public decimal AidatIncome { get; set; }
    }

    public class DaireWithDetails
    {
        public Daire Daire { get; set; } = new();
        public string SiteName { get; set; } = string.Empty;
        public string ApartmentName { get; set; } = string.Empty;
        public decimal TotalDebt { get; set; }
    }
}
