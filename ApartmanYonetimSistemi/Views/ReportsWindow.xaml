<Window x:Class="ApartmanYonetimSistemi.Views.ReportsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
        Title="Raporlar ve İstatistikler" 
        Height="800" Width="1400"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">
    
    <Window.DataContext>
        <vm:ReportsViewModel />
    </Window.DataContext>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2E86AB" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="📊" FontSize="24" Margin="0,0,10,0"/>
                    <StackPanel>
                        <TextBlock Text="Raporlar ve İstatistikler" 
                                  FontSize="20" FontWeight="Bold" 
                                  Foreground="White"/>
                        <TextBlock Text="Gelir raporları, borçlu daireler ve ödeme geçmişi" 
                                  FontSize="14" 
                                  Foreground="#E3F2FD"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="🔄 Yenile" 
                            Command="{Binding RefreshReportsCommand}"
                            Background="#4CAF50" Foreground="White"
                            BorderThickness="0" Padding="15,8"
                            FontSize="12" FontWeight="SemiBold" Cursor="Hand" Margin="0,0,10,0">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="4" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" 
                                                                VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#45A049"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                    
                    <Button Content="📄 Excel Export" 
                            Command="{Binding ExportToExcelCommand}"
                            Background="#FF9800" Foreground="White"
                            BorderThickness="0" Padding="15,8"
                            FontSize="12" FontWeight="SemiBold" Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="4" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" 
                                                                VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#F57C00"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Summary Cards -->
            <Grid Grid.Row="0" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Monthly Income -->
                <Border Grid.Column="0" Background="White" CornerRadius="8" Padding="25" Margin="0,0,10,0"
                        Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="💰" FontSize="32" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding TotalMonthlyIncome, StringFormat={}{0:C}}" FontSize="24" FontWeight="Bold" 
                                  Foreground="#4CAF50" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                        <TextBlock Text="Bu Ay Toplam Gelir" FontSize="14" Foreground="#666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Yearly Income -->
                <Border Grid.Column="1" Background="White" CornerRadius="8" Padding="25" Margin="0,0,10,0"
                        Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="📈" FontSize="32" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding TotalYearlyIncome, StringFormat={}{0:C}}" FontSize="24" FontWeight="Bold" 
                                  Foreground="#2E86AB" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                        <TextBlock Text="Yıllık Toplam Gelir" FontSize="14" Foreground="#666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Borclu Daireler -->
                <Border Grid.Column="2" Background="White" CornerRadius="8" Padding="25"
                        Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="⚠️" FontSize="32" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding BorcluDaireler.Count}" FontSize="24" FontWeight="Bold" 
                                  Foreground="#F44336" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                        <TextBlock Text="Borçlu Daire" FontSize="14" Foreground="#666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Tabs -->
            <TabControl Grid.Row="1" Background="Transparent" BorderThickness="0">
                <TabControl.Resources>
                    <Style TargetType="TabItem">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="TabItem">
                                    <Border Name="Border" Background="White" BorderBrush="#E0E0E0" BorderThickness="1,1,1,0" 
                                            CornerRadius="8,8,0,0" Padding="20,10" Margin="0,0,5,0">
                                        <ContentPresenter ContentSource="Header" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="#2E86AB"/>
                                            <Setter Property="Foreground" Value="White"/>
                                        </Trigger>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="#E3F2FD"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </TabControl.Resources>

                <!-- Monthly Reports Tab -->
                <TabItem Header="📊 Aylık Gelir Raporları">
                    <Border Background="White" CornerRadius="0,8,8,8" Padding="20"
                            Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                        <Grid>
                            <!-- Loading -->
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                                       Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <TextBlock Text="⏳" FontSize="32" HorizontalAlignment="Center"/>
                                <TextBlock Text="Raporlar yükleniyor..." FontSize="14" 
                                          Foreground="#666666" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                            </StackPanel>

                            <!-- Reports List -->
                            <ScrollViewer VerticalScrollBarVisibility="Auto"
                                         Visibility="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}">
                                <ItemsControl ItemsSource="{Binding MonthlyReports}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="1"
                                                    CornerRadius="6" Margin="0,0,0,10" Padding="20">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBlock Grid.Column="0" Text="{Binding MonthName}" 
                                                              FontSize="16" FontWeight="SemiBold" VerticalAlignment="Center"/>

                                                    <StackPanel Grid.Column="1" Margin="20,0">
                                                        <TextBlock Text="Kira" FontSize="11" Foreground="#666666" HorizontalAlignment="Center"/>
                                                        <TextBlock Text="{Binding KiraIncome, StringFormat={}{0:C}}" 
                                                                  FontSize="14" FontWeight="SemiBold" Foreground="#4CAF50" HorizontalAlignment="Center"/>
                                                    </StackPanel>

                                                    <StackPanel Grid.Column="2" Margin="20,0">
                                                        <TextBlock Text="Aidat" FontSize="11" Foreground="#666666" HorizontalAlignment="Center"/>
                                                        <TextBlock Text="{Binding AidatIncome, StringFormat={}{0:C}}" 
                                                                  FontSize="14" FontWeight="SemiBold" Foreground="#FF9800" HorizontalAlignment="Center"/>
                                                    </StackPanel>

                                                    <StackPanel Grid.Column="3" Margin="20,0">
                                                        <TextBlock Text="Toplam" FontSize="11" Foreground="#666666" HorizontalAlignment="Center"/>
                                                        <TextBlock Text="{Binding TotalIncome, StringFormat={}{0:C}}" 
                                                                  FontSize="16" FontWeight="Bold" Foreground="#2E86AB" HorizontalAlignment="Center"/>
                                                    </StackPanel>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>
                        </Grid>
                    </Border>
                </TabItem>

                <!-- Borclu Daireler Tab -->
                <TabItem Header="⚠️ Borçlu Daireler">
                    <Border Background="White" CornerRadius="0,8,8,8" Padding="20"
                            Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <ItemsControl ItemsSource="{Binding BorcluDaireler}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="#FFF3E0" BorderBrush="#FFB74D" BorderThickness="1"
                                                CornerRadius="6" Margin="0,0,0,10" Padding="15">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <StackPanel Grid.Column="0">
                                                    <TextBlock Text="{Binding SiteName}" FontWeight="SemiBold" FontSize="14"/>
                                                    <TextBlock Text="{Binding ApartmentName}" FontSize="12" Foreground="#666666"/>
                                                    <TextBlock Text="{Binding Daire.DaireNo, StringFormat={}Daire {0}}" FontSize="12" Foreground="#666666"/>
                                                    <TextBlock Text="{Binding Daire.Kiraci.Ad, StringFormat={}Kiracı: {0}, TargetNullValue=Kiracı: Boş}" 
                                                              FontSize="11" Foreground="#666666"/>
                                                </StackPanel>

                                                <StackPanel Grid.Column="1" Margin="20,0" VerticalAlignment="Center">
                                                    <TextBlock Text="Borç Durumu" FontSize="11" Foreground="#666666" HorizontalAlignment="Center"/>
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                        <Border Background="#F44336" CornerRadius="10" Padding="6,2" Margin="2"
                                                                Visibility="{Binding Daire.KiraDurumu, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter=Borclu}">
                                                            <TextBlock Text="Kira" Foreground="White" FontSize="9"/>
                                                        </Border>
                                                        <Border Background="#F44336" CornerRadius="10" Padding="6,2" Margin="2"
                                                                Visibility="{Binding Daire.AidatDurumu, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter=Borclu}">
                                                            <TextBlock Text="Aidat" Foreground="White" FontSize="9"/>
                                                        </Border>
                                                    </StackPanel>
                                                </StackPanel>

                                                <StackPanel Grid.Column="2" VerticalAlignment="Center">
                                                    <TextBlock Text="Toplam Borç" FontSize="11" Foreground="#666666" HorizontalAlignment="Center"/>
                                                    <TextBlock Text="{Binding TotalDebt, StringFormat={}{0:C}}" 
                                                              FontSize="16" FontWeight="Bold" Foreground="#F44336" HorizontalAlignment="Center"/>
                                                </StackPanel>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Border>
                </TabItem>

                <!-- Recent Payments Tab -->
                <TabItem Header="💰 Son Ödemeler">
                    <Border Background="White" CornerRadius="0,8,8,8" Padding="20"
                            Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <ItemsControl ItemsSource="{Binding RecentPayments}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="1"
                                                CornerRadius="6" Margin="0,0,0,8" Padding="15">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <StackPanel Grid.Column="0">
                                                    <TextBlock Text="{Binding Tip}" FontWeight="SemiBold" FontSize="14"/>
                                                    <TextBlock Text="{Binding Tarih, StringFormat={}{0:dd.MM.yyyy HH:mm}}" 
                                                              FontSize="12" Foreground="#666666"/>
                                                </StackPanel>

                                                <TextBlock Grid.Column="1" Text="{Binding Tutar, StringFormat={}{0:C}}" 
                                                          FontWeight="SemiBold" FontSize="14" VerticalAlignment="Center" Margin="20,0"/>

                                                <Border Grid.Column="2" CornerRadius="12" Padding="8,4" VerticalAlignment="Center" Margin="10,0">
                                                    <Border.Style>
                                                        <Style TargetType="Border">
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding OdemeDurumu}" Value="Odendi">
                                                                    <Setter Property="Background" Value="#4CAF50"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding OdemeDurumu}" Value="Borclu">
                                                                    <Setter Property="Background" Value="#F44336"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Border.Style>
                                                    <TextBlock Text="{Binding OdemeDurumu}" Foreground="White" 
                                                              FontSize="10" FontWeight="SemiBold"/>
                                                </Border>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Border>
                </TabItem>
            </TabControl>
        </Grid>
    </Grid>
</Window>
